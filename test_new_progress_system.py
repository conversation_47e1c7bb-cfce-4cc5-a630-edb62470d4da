#!/usr/bin/env python3
"""
Test script to verify the new asynchronous progress tracking system.
"""

import threading
import time

def test_new_progress_system():
    """Test the new progress tracking system"""
    
    print("Testing New Asynchronous Progress System")
    print("=" * 50)
    
    # Simulate the progress state structure
    progress_state = {
        'current_progress': 0.0,
        'extraction_complete': False,
        'stitching_complete': False,
        'total_frames_to_extract': 100,
        'frames_extracted': 0,
        'total_frames_to_stitch': 100,
        'frames_stitched': 0,
        'extraction_end_percent': 70.0,  # 70/30 split
        'lock': threading.Lock()
    }
    
    progress_updates = []
    
    def mock_progress_callback(progress):
        progress_updates.append(progress)
        print(f"Progress: {progress:.1f}%")
    
    def update_progress_safe(new_progress):
        """Thread-safe progress update that only moves forward."""
        try:
            with progress_state['lock']:
                # Only update if progress moves forward
                if new_progress > progress_state['current_progress']:
                    progress_state['current_progress'] = new_progress
                    # Ensure progress doesn't exceed 100%
                    final_progress = min(100, max(0, new_progress))
                    mock_progress_callback(final_progress)
        except Exception as e:
            print(f"Error in progress update: {e}")
    
    def update_extraction_progress(frames_extracted):
        """Update progress based on frames extracted."""
        try:
            with progress_state['lock']:
                progress_state['frames_extracted'] = frames_extracted
                
                if progress_state['total_frames_to_extract'] > 0:
                    extraction_percent = (frames_extracted / progress_state['total_frames_to_extract']) * 100
                    # Map to extraction phase (0 to extraction_end_percent)
                    total_progress = (extraction_percent / 100.0) * progress_state['extraction_end_percent']
                    update_progress_safe(total_progress)
        except Exception as e:
            print(f"Error updating extraction progress: {e}")
    
    def update_stitching_progress(frames_stitched):
        """Update progress based on frames stitched."""
        try:
            with progress_state['lock']:
                progress_state['frames_stitched'] = frames_stitched
                
                if progress_state['total_frames_to_stitch'] > 0:
                    stitching_percent = (frames_stitched / progress_state['total_frames_to_stitch']) * 100
                    # Map to stitching phase (extraction_end_percent to 100%)
                    stitching_range = 100.0 - progress_state['extraction_end_percent']
                    total_progress = progress_state['extraction_end_percent'] + (stitching_percent / 100.0) * stitching_range
                    update_progress_safe(total_progress)
        except Exception as e:
            print(f"Error updating stitching progress: {e}")
    
    def mark_extraction_complete():
        """Mark extraction phase as complete."""
        try:
            with progress_state['lock']:
                progress_state['extraction_complete'] = True
                update_progress_safe(progress_state['extraction_end_percent'])
                print(f"Extraction phase complete at {progress_state['extraction_end_percent']}%")
        except Exception as e:
            print(f"Error marking extraction complete: {e}")
    
    def mark_stitching_complete():
        """Mark stitching phase as complete."""
        try:
            with progress_state['lock']:
                progress_state['stitching_complete'] = True
                update_progress_safe(100.0)
                print("Stitching phase complete at 100%")
        except Exception as e:
            print(f"Error marking stitching complete: {e}")
    
    # Test extraction phase
    print("\n1. Testing Extraction Phase (0-70%):")
    for frames in [10, 25, 50, 75, 100]:
        update_extraction_progress(frames)
        time.sleep(0.1)  # Small delay to simulate real processing
    
    # Mark extraction complete
    print("\n2. Marking Extraction Complete:")
    mark_extraction_complete()
    
    # Test stitching phase
    print("\n3. Testing Stitching Phase (70-100%):")
    for frames in [10, 25, 50, 75, 100]:
        update_stitching_progress(frames)
        time.sleep(0.1)  # Small delay to simulate real processing
    
    # Mark stitching complete
    print("\n4. Marking Stitching Complete:")
    mark_stitching_complete()
    
    print(f"\nFinal Progress: {progress_state['current_progress']:.1f}%")
    print(f"Total Progress Updates: {len(progress_updates)}")
    print(f"Progress Values: {[f'{p:.1f}' for p in progress_updates]}")
    
    # Test monotonic property (progress should never go backward)
    is_monotonic = all(progress_updates[i] <= progress_updates[i+1] for i in range(len(progress_updates)-1))
    print(f"Progress is Monotonic (never goes backward): {is_monotonic}")
    
    # Test final value
    reaches_100 = progress_state['current_progress'] == 100.0
    print(f"Reaches 100%: {reaches_100}")
    
    return is_monotonic and reaches_100

if __name__ == "__main__":
    success = test_new_progress_system()
    print(f"\nTest Result: {'PASS' if success else 'FAIL'}")
