"""
Image processing utilities for the Tire Panorama Tool.
"""
import os
import time
import traceback
import subprocess
import sys
import glob
import re
from PIL import Image, ImageTk, ImageFile
from tkinter import messagebox

# Disable PIL decompression bomb warning for large panorama images
ImageFile.LOAD_TRUNCATED_IMAGES = True
Image.MAX_IMAGE_PIXELS = None

# Helper function to get ffmpeg path
def get_ffmpeg_path():
    """Determines the correct path to ffmpeg executable."""
    ffmpeg_exe_name = "ffmpeg.exe" if os.name == 'nt' else "ffmpeg"
    # Path for PyInstaller bundle
    if hasattr(sys, '_MEIPASS'):
        # In a PyInstaller bundle, ffmpeg_bin should be at the root of _MEIPASS
        return os.path.join(sys._MEIPASS, "ffmpeg_bin", ffmpeg_exe_name)

    # Path for development (running as .py script)
    # Assumes image_utils.py is in TireStitcher/core/
    # and ffmpeg_bin is in TireStitcher/ffmpeg_bin/
    script_dir = os.path.dirname(os.path.abspath(__file__))  # TireStitcher/core
    project_root = os.path.dirname(script_dir)  # TireStitcher
    return os.path.join(project_root, "ffmpeg_bin", ffmpeg_exe_name)

FFMPEG_PATH = get_ffmpeg_path()

def extract_frames_opencv(video_path, output_folder, frame_step=1, status_callback=None):
    """
    Extract frames from a video file using OpenCV.

    Args:
        video_path: Path to the video file
        output_folder: Directory to save frames
        frame_step: Extract every Nth frame
        status_callback: Function to call with status updates

    Returns:
        True if successful, False otherwise
    """
    try:
        import cv2

        # Start timing
        start_time = time.time()

        # Open the video file
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise Exception(f"Could not open video file {video_path}")

        # Get video properties
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        fps = cap.get(cv2.CAP_PROP_FPS)

        print(f"Video properties: {frame_count} frames, {fps} FPS")

        # Make sure output folder exists
        os.makedirs(output_folder, exist_ok=True)

        frame_number = 0
        saved_frames = 0
        last_update_time = start_time

        while True:
            ret, frame = cap.read()
            if not ret:
                break

            if frame_number % frame_step == 0:
                # Save the frame
                output_path = os.path.join(output_folder, f"IMG_{saved_frames + 1:04d}.JPG")
                cv2.imwrite(output_path, frame)
                saved_frames += 1

                # Update progress
                if frame_count > 0:
                    progress_percent = int((frame_number / frame_count) * 100)

                    # Report progress every 10 frames or 5 seconds
                    current_time = time.time()
                    if saved_frames % 10 == 0 or (current_time - last_update_time) > 5:
                        # Calculate estimated remaining time
                        elapsed_time = current_time - start_time
                        if frame_number > 0:
                            frames_per_second = frame_number / elapsed_time
                            remaining_frames = frame_count - frame_number
                            estimated_remaining_seconds = remaining_frames / frames_per_second

                            # Format remaining time
                            mins = int(estimated_remaining_seconds // 60)
                            secs = int(estimated_remaining_seconds % 60)
                            eta = f"{mins}m {secs}s"

                            if status_callback:
                                status_callback(
                                    saved_frames,
                                    frame_count,
                                    progress_percent,
                                    eta,
                                    frames_per_second
                                )

                            last_update_time = current_time

            frame_number += 1

        # Final update
        total_time = time.time() - start_time
        mins = int(total_time // 60)
        secs = int(total_time % 60)

        if status_callback:
            status_callback(saved_frames, frame_count, 100, f"{mins}m {secs}s", 0)

        cap.release()
        return True

    except Exception as e:
        print(f"Error in frame extraction: {str(e)}")
        traceback.print_exc()
        return False

def extract_frames_ffmpeg(video_path, output_folder, frame_step=1, rotate=True, samples=None, max_frames=None, status_callback=None, process_cancelled_check=None, start_frame=0, frame_callback=None, process_ref_callback=None):
    """
    Extract frames from a video file using direct FFmpeg command.

    Args:
        video_path: Path to the video file
        output_folder: Directory to save frames
        frame_step: Extract every Nth frame
        rotate: Whether to rotate frames 90 degrees
        samples: Alternative way to specify sample rate (overrides frame_step if provided)
        max_frames: Maximum number of frames to extract
        status_callback: Function to call with status updates
        process_cancelled_check: Function that returns True if process should be cancelled
        start_frame: Frame index to start extraction from (for continuing after cancellation)
        frame_callback: Function to call with current frame index updates for state tracking
        process_ref_callback: Function to call with the process reference for cancellation

    Returns:
        True if successful, False otherwise
    """
    try:
        # Start timing
        start_time_val = time.time()

        # Make sure output folder exists
        os.makedirs(output_folder, exist_ok=True)

        if status_callback:
            status_callback(f"Preparing FFmpeg extraction from: {video_path}")

        # Check if ffmpeg is available
        try:
            process_creation_flags = subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            subprocess.run([FFMPEG_PATH, "-version"], check=True, capture_output=True, creationflags=process_creation_flags)
        except (subprocess.SubprocessError, FileNotFoundError) as e:
            error_message = f"FFmpeg not found or failed to run from '{FFMPEG_PATH}'. Error: {e}. Please ensure it is correctly placed in the ffmpeg_bin directory and is executable."
            if status_callback:
                status_callback(error_message)
            print(error_message)
            if messagebox:
                messagebox.showerror("FFmpeg Error", error_message)
            return False

        # Build the FFmpeg command
        output_pattern = os.path.join(output_folder, "IMG_%04d.JPG")

        # Base command
        cmd = [
            FFMPEG_PATH,
            "-i", video_path,
            "-q:v", "1",
            "-pix_fmt", "yuvj420p"
        ]

        # If we're continuing from a specific frame, add the start time parameter
        if start_frame > 0:
            fps = 30
            try:
                import cv2
                cap = cv2.VideoCapture(video_path)
                if cap.isOpened():
                    fps = cap.get(cv2.CAP_PROP_FPS)
                    if fps <= 0:
                        fps = 30
                    cap.release()
            except Exception as e:
                print(f"Error getting video FPS: {e}")

            start_time_seconds = start_frame / fps
            cmd.extend(["-ss", f"{start_time_seconds:.3f}"])

            if status_callback:
                status_callback(f"Continuing extraction from frame {start_frame} (approx. {start_time_seconds:.2f} seconds)")

        actual_frame_step = frame_step
        if samples and samples != 1:
            try:
                actual_frame_step = int(samples)
            except (ValueError, TypeError):
                pass

        if max_frames and int(max_frames) > 0:
            cmd.extend(["-vframes", str(max_frames)])

        filter_commands = []

        if rotate:
            filter_commands.append("transpose=1")

        scaleSize = 0.02
        if actual_frame_step > 1:
           scaleSize = actual_frame_step * 0.02
           if scaleSize > 0.3:
               scaleSize = 0.3

        filter_commands.append(f"crop=iw*{scaleSize}:ih:(iw-iw*{scaleSize})/2:0")

        if actual_frame_step > 1:
            filter_commands.append(f"select=not(mod(n,{actual_frame_step}))")
        if filter_commands:
            cmd.extend(["-vf", ",".join(filter_commands)])
            cmd.extend(["-vsync", "vfr"])

        cmd.extend(["-f", "image2", output_pattern])

        if status_callback:
            status_callback(f"Starting FFmpeg frame extraction")

        print(f"Running FFmpeg command: {' '.join(cmd)}")

        process_creation_flags = subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1,
            creationflags=process_creation_flags
        )

        if process_ref_callback:
            process_ref_callback(process)

        last_count = 0
        last_update_time = time.time()
        frame_pattern = re.compile(r"frame=\s*(\d+)")
        fps_pattern = re.compile(r"fps=\s*(\d+\.?\d*)")
        time_pattern = re.compile(r"time=(\d+):(\d+):(\d+\.\d+)")

        while process.poll() is None:
            if process_cancelled_check and process_cancelled_check():
                print("Frame extraction cancelled by user, terminating process...")
                from core.process_utils import terminate_process

                terminate_success = terminate_process(process)

                if not terminate_success:
                    print(f"Initial termination failed for process {process.pid}, trying Windows-specific methods")

                    try:
                        subprocess.run(
                            ['taskkill', '/F', '/T', '/PID', str(process.pid)],
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE,
                            check=False
                        )
                    except Exception:
                        print(f"Error using Windows taskkill for PID {process.pid}")

                if status_callback:
                    status_callback("Frame extraction cancelled by user")
                return False

            line = process.stderr.readline().strip()
            if not line:
                time.sleep(0.1)
                continue

            print(line)

            current_time = time.time()
            frame_match = frame_pattern.search(line)
            fps_match = fps_pattern.search(line)
            time_match = time_pattern.search(line)

            if (frame_match or time_match) and (current_time - last_update_time) > 1:
                if frame_match:
                    current_frame = int(frame_match.group(1))

                    if frame_callback:
                        frame_callback(current_frame)

                    current_fps = None
                    if fps_match:
                        current_fps = float(fps_match.group(1))

                    extracted_frames = current_frame if frame_step == 1 else current_frame // frame_step

                    # Calculate progress based on estimated total frames
                    progress_percent = None
                    eta_text = "unknown"

                    # Try to estimate progress based on video duration and current time
                    if time_match:
                        h = int(time_match.group(1))
                        m = int(time_match.group(2))
                        s = float(time_match.group(3))
                        elapsed_seconds = h * 3600 + m * 60 + s

                        # Estimate total video duration and calculate progress
                        try:
                            import cv2
                            cap = cv2.VideoCapture(video_path)
                            if cap.isOpened():
                                total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                                fps = cap.get(cv2.CAP_PROP_FPS)
                                if fps > 0:
                                    total_duration = total_frames / fps
                                    progress_percent = min(int((elapsed_seconds / total_duration) * 100), 100)

                                    # Calculate ETA
                                    if progress_percent > 0:
                                        remaining_duration = total_duration - elapsed_seconds
                                        eta_minutes = int(remaining_duration // 60)
                                        eta_seconds = int(remaining_duration % 60)
                                        eta_text = f"{eta_minutes}m {eta_seconds}s"
                                cap.release()
                        except Exception:
                            pass

                    if status_callback:
                        status_callback(
                            f"Extracting frames: {extracted_frames} frames processed",
                            extracted_frames,
                            None,
                            eta_text,
                            current_fps,
                            progress_percent
                        )

                    last_update_time = current_time
                    last_count = extracted_frames

            if (current_time - last_update_time) > 5:
                frame_files = glob.glob(os.path.join(output_folder, "IMG_*.JPG"))
                frame_count = len(frame_files)

                if frame_count > 0 and frame_count != last_count:
                    if status_callback:
                        status_callback(f"Extracted {frame_count} frames so far")
                    last_count = frame_count
                    last_update_time = current_time

        if process.returncode != 0:
            stderr_output = process.stderr.read()
            error_msg = f"FFmpeg failed with error code {process.returncode}. Stderr:\n{stderr_output}"
            if status_callback:
                status_callback(error_msg)
            print(error_msg)
            if messagebox:
                messagebox.showerror("FFmpeg Execution Error", f"FFmpeg failed: {stderr_output[:500]}")
            return False

        frame_files = glob.glob(os.path.join(output_folder, "IMG_*.JPG"))
        frame_count = len(frame_files)

        if frame_count == 0:
            raise Exception("No frames were extracted from the video")

        total_time = time.time() - start_time_val
        minutes = int(total_time // 60)
        seconds = int(total_time % 60)

        message = f"Completed frame extraction: {frame_count} frames in {minutes}m {seconds}s"
        print(message)

        if status_callback:
            status_callback(message, frame_count, frame_count, "0s", 0, 100)

        return True

    except Exception as e:
        error_msg = f"Error in FFmpeg frame extraction: {str(e)}"
        print(error_msg)
        traceback.print_exc()

        if status_callback:
            status_callback(error_msg)

        if messagebox:
            messagebox.showerror("Unexpected FFmpeg Error", f"{error_msg[:1000]}")
        return False

def extract_frames_using_external(executable_path, video_path, output_folder,
                                 rotate=True, samples="", status_callback=None,
                                 process_cancelled_check=None, max_frames=None):
    """
    Extract frames from a video using an external executable.

    Args:
        executable_path: Path to the frame extraction executable
        video_path: Path to the video file
        output_folder: Directory to save frames
        rotate: Whether to rotate frames 90 degrees
        samples: Sample rate for frame extraction
        status_callback: Function to call with status updates
        process_cancelled_check: Function that returns True if process should be cancelled

    Returns:
        True if successful, False otherwise
    """
    try:
        if not executable_path or not os.path.exists(executable_path):
            if status_callback:
                status_callback("extract_frames executable not found, using fallback method")
            return False

        if status_callback:
            status_callback(f"Using {os.path.basename(executable_path)} for frame extraction...")

        # Create command for extract_frames
        cmd = [
            executable_path,
            video_path,
            output_folder
        ]

        if rotate:
            cmd.append("--rotate")

        if samples:
            cmd.extend(["--samples", samples])

        if max_frames and int(max_frames) > 0:
            cmd.extend(["--max-frames", str(max_frames)])

        print(f"Running frame extraction command: {' '.join(cmd)}")

        total_frames = 0
        try:
            import cv2
            cap = cv2.VideoCapture(video_path)
            if cap.isOpened():
                total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                sample_rate = int(samples) if samples and samples.isdigit() else 1
                expected_frames = total_frames // sample_rate
                cap.release()
                print(f"Video contains {total_frames} frames, expected extraction: ~{expected_frames} frames")
        except Exception as e:
            print(f"Error determining frame count: {e}")
            total_frames = 0

        start_time = time.time()
        last_update_time = start_time
        last_frames_count = 0

        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1,
            creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
        )

        for line in iter(process.stdout.readline, ''):
            if process_cancelled_check and process_cancelled_check():
                print("Frame extraction cancelled by user, terminating process...")
                from core.process_utils import terminate_process

                terminate_success = terminate_process(process)

                if not terminate_success:
                    print(f"Initial termination failed for process {process.pid}, trying Windows-specific methods")

                    try:
                        subprocess.run(
                            ['taskkill', '/F', '/T', '/PID', str(process.pid)],
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE,
                            check=False
                        )
                    except Exception:
                        print(f"Error using Windows taskkill for PID {process.pid}")

                if status_callback:
                    status_callback("Frame extraction cancelled by user")
                return False

            line = line.strip()
            print(line)

            if "frames saved" in line:
                try:
                    frames_count = int(line.split()[0])

                    if total_frames > 0:
                        sample_rate = int(samples) if samples and samples.isdigit() else 1
                        expected_frames = total_frames // sample_rate
                        progress_percent = min(int(frames_count * 100 / expected_frames), 100)

                        current_time = time.time()
                        elapsed_time = current_time - start_time
                        frames_since_last_update = frames_count - last_frames_count

                        if (current_time - last_update_time > 2.0 or frames_since_last_update >= 5) and frames_count > 0:
                            frames_per_second = frames_count / elapsed_time if elapsed_time > 0 else 0

                            if expected_frames > 0 and frames_per_second > 0:
                                remaining_frames = expected_frames - frames_count
                                remaining_seconds = remaining_frames / frames_per_second if frames_per_second > 0 else 0

                                minutes = int(remaining_seconds // 60)
                                seconds = int(remaining_seconds % 60)

                                eta_text = f"{minutes}m {seconds}s"
                                if status_callback:
                                    status_callback(
                                        f"Extracting frames: {frames_count}/{expected_frames}",
                                        frames_count,
                                        expected_frames,
                                        eta_text,
                                        frames_per_second,
                                        progress_percent
                                    )
                            else:
                                if status_callback:
                                    status_callback(
                                        f"Extracting frames: {frames_count}",
                                        frames_count,
                                        None,
                                        None,
                                        frames_per_second,
                                        None
                                    )

                            last_update_time = current_time
                            last_frames_count = frames_count

                except Exception as e:
                    print(f"Error calculating frame count and ETA: {e}")
                    traceback.print_exc()

            if "Progress:" in line and "%" in line:
                try:
                    progress_percent = int(line.split(":")[1].split("%")[0].strip())
                    if status_callback:
                        status_callback(
                            f"Extracting frames: {progress_percent}%",
                            None, None, None, None, progress_percent
                        )
                except Exception as e:
                    print(f"Error processing progress indicator: {e}")

        try:
            process.wait(timeout=900)
        except subprocess.TimeoutExpired:
            process.kill()
            raise Exception("Frame extraction process timed out after 15 minutes")

        if process.returncode != 0 and not (process_cancelled_check and process_cancelled_check()):
            stderr_output = process.stderr.read()
            error_message = f"Frame extraction failed with code {process.returncode}. Stderr:\n{stderr_output}"
            print(f"Frame extraction error output:\n{stderr_output}")
            raise Exception(error_message)

        if process_cancelled_check and process_cancelled_check():
            return False

        extracted_files = [f for f in os.listdir(output_folder) if f.endswith('.JPG') or f.endswith('.jpg')]

        if not extracted_files:
            raise Exception("No frames were extracted to the output folder")

        total_elapsed = time.time() - start_time
        minutes = int(total_elapsed // 60)
        seconds = int(total_elapsed % 60)

        print(f"Successfully extracted {len(extracted_files)} frames to {output_folder} in {minutes}m {seconds}s")
        if status_callback:
            status_callback(
                f"Extracted {len(extracted_files)} frames in {minutes}m {seconds}s",
                len(extracted_files),
                len(extracted_files),
                "Done",
                0,
                100
            )

        return True

    except Exception as e:
        error_message = str(e)
        error_traceback = traceback.format_exc()
        if 'process' in locals() and hasattr(process, 'stderr') and process.stderr:
            try:
                if not process.stderr.closed and process.stderr.readable():
                    stderr_content = process.stderr.read()
                    if stderr_content:
                        error_message += f"\nAdditional Stderr from process: {stderr_content}"
                        print(f"Additional Stderr from process (if available):\n{stderr_content}")
            except Exception as e_stderr:
                print(f"Error reading stderr from process: {e_stderr}")

        print(f"Error in frame extraction: {error_message}\n{error_traceback}")
        if status_callback:
            status_callback(f"Error extracting frames: {error_message}")
        return False

def load_image_scaled_to_height(image_path, target_height):
    """
    Load an image and scale it to the specified height while maintaining aspect ratio.

    Args:
        image_path: Path to the image file
        target_height: Desired height in pixels

    Returns:
        PIL.ImageTk.PhotoImage object
    """
    try:
        with Image.open(image_path) as img_info:
            img_width, img_height = img_info.size

        scale = target_height / img_height
        new_width = int(img_width * scale)
        new_height = target_height

        img = Image.open(image_path)
        img = img.resize((new_width, new_height), Image.LANCZOS)

        photo = ImageTk.PhotoImage(img)
        return photo, img_width, img_height, new_width, new_height

    except Exception as e:
        print(f"Error loading image: {e}")
        traceback.print_exc()
        return None, 0, 0, 0, 0

def extract_frames_direct(video_path, output_folder, frame_step=1, rotate=True, status_callback=None, process_cancelled_check=None):
    """
    Extract frames from a video file directly using OpenCV with high quality settings.

    Args:
        video_path: Path to the video file
        output_folder: Directory to save frames
        frame_step: Extract every Nth frame
        rotate: Whether to rotate frames 90 degrees
        status_callback: Function to call with status updates
        process_cancelled_check: Function that returns True if process should be cancelled

    Returns:
        True if successful, False otherwise
    """
    try:
        import cv2
        import os
        import time

        start_time = time.time()

        os.makedirs(output_folder, exist_ok=True)

        if status_callback:
            status_callback(f"Opening video file: {video_path}")

        cap = cv2.VideoCapture(video_path, cv2.CAP_FFMPEG)

        if not cap.isOpened():
            raise Exception(f"Could not open video file: {video_path}")

        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        fps = cap.get(cv2.CAP_PROP_FPS)

        print(f"Video properties: {frame_count} frames, {width}x{height} at {fps} FPS")
        if status_callback:
            status_callback(f"Extracting frames from video: {frame_count} frames, {width}x{height} at {fps} FPS")

        frame_number = 0
        saved_frames = 0
        last_update_time = start_time

        jpeg_params = [cv2.IMWRITE_JPEG_QUALITY, 98]

        while True:
            ret, frame = cap.read()

            if not ret:
                break

            if frame_number % frame_step == 0:
                if process_cancelled_check and process_cancelled_check():
                    print("Frame extraction cancelled by user, stopping extraction...")
                    cap.release()

                    try:
                        import gc
                        gc.collect()
                    except Exception as e:
                        print(f"Error during garbage collection: {e}")

                    if status_callback:
                        status_callback("Frame extraction cancelled by user")
                    return False

                if rotate:
                    frame = cv2.rotate(frame, cv2.ROTATE_90_CLOCKWISE)

                output_path = os.path.join(output_folder, f"IMG_{saved_frames+1:04d}.JPG")

                success = cv2.imwrite(output_path, frame, jpeg_params)

                if not success:
                    print(f"Warning: Failed to write frame to {output_path}")

                    png_path = os.path.join(output_folder, f"IMG_{saved_frames+1:04d}.PNG")
                    success = cv2.imwrite(png_path, frame)

                    if success:
                        print(f"Successfully saved as PNG instead: {png_path}")
                        output_path = png_path

                if not os.path.exists(output_path):
                    print(f"Warning: File was not created at {output_path}")
                    success = cv2.imwrite(output_path, frame, [cv2.IMWRITE_JPEG_QUALITY, 95])

                    if not success:
                        print(f"Error: Could not write frame to disk after multiple attempts")
                        continue

                saved_frames += 1

                current_time = time.time()
                if saved_frames % 10 == 0 or (current_time - last_update_time) > 2.0:
                    progress = min(int((frame_number / frame_count) * 100), 100) if frame_count > 0 else 0
                    elapsed = current_time - start_time
                    frames_per_sec = frame_number / elapsed if elapsed > 0 else 0

                    if frames_per_sec > 0 and frame_count > 0:
                        remaining_frames = frame_count - frame_number
                        eta_seconds = remaining_frames / frames_per_sec
                        eta_min = int(eta_seconds // 60)
                        eta_sec = int(eta_seconds % 60)
                        eta_text = f"{eta_min}m {eta_sec}s"
                    else:
                        eta_text = "unknown"

                    if status_callback:
                        status_callback(
                            f"Extracted {saved_frames} frames ({frames_per_sec:.1f} fps)",
                            saved_frames,
                            frame_count // frame_step,
                            eta_text,
                            frames_per_sec,
                            progress
                        )

                    last_update_time = current_time

            frame_number += 1

        cap.release()

        total_time = time.time() - start_time
        minutes = int(total_time // 60)
        seconds = int(total_time % 60)

        print(f"Extracted {saved_frames} frames in {minutes}m {seconds}s")

        if saved_frames == 0:
            raise Exception("No frames were extracted from the video")

        if status_callback:
            status_callback(
                f"Completed frame extraction: {saved_frames} frames in {minutes}m {seconds}s",
                saved_frames,
                saved_frames,
                "0s",
                0,
                100
            )

        return True

    except Exception as e:
        import traceback
        print(f"Error in frame extraction: {str(e)}")
        traceback.print_exc()
        if status_callback:
            status_callback(f"Error extracting frames: {str(e)}")
        return False