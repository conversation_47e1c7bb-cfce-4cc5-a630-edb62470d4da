"""
Main application class for the Tire Panorama Tool.
"""
import os
import sys
import traceback
from datetime import datetime
import tkinter as tk
from tkinter import filedialog, messagebox
import subprocess  # Added for ffprobe
import json  # Added for ffprobe

from utils.cache_cleanup import add_rebuild_button
from core.panorama_processor import PanoramaProcessor
from core.file_utils import find_existing_panoramas
from gui.ui_factory import create_ui, setup_sidebar_styles
from gui.process_manager import update_status, update_progress, process_completed
from gui.utils import shorten_path
from gui.process_manager import update_status, update_progress, process_completed

# Import new modules
from managers.plan_manager import PlanManager
from managers.project_manager import ProjectManager
from managers.subproject_manager import SubprojectManager
from managers.process_queue_manager import ProcessQueueManager

class TirePanoramaApp:
    """
    Main application class for the Tire Panorama Tool.
    """
    def __init__(self, root):
        """
        Initialize the application.

        Args:
            root: Tkinter root window
        """
        self.root = root
        self.root.title("Tire Panorama Tool")
        self.root.geometry("1200x800")
        self.root.configure(bg="#f5f5f5")

        # Determine base path for bundled application
        if getattr(sys, 'frozen', False):
            # If the application is run as a bundle, the base path is the temp directory created by PyInstaller
            if hasattr(sys, '_MEIPASS'):
                self.application_path = sys._MEIPASS # one-file bundle
            else:
                self.application_path = os.path.dirname(sys.executable) # one-folder bundle
        else:
            # If run from a script, the base path is the directory containing main.py
            self.application_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__))) # Up two levels from gui/app.py to TireStitcher/

        # Standard input directory (relative to where main.py or the bundle root is)
        self.input_base_dir = os.path.join(self.application_path, "input")
        if not os.path.exists(self.input_base_dir):
            os.makedirs(self.input_base_dir, exist_ok=True)

        # Custom video folder path (will be used for file dialogs if set)
        self.custom_video_folder = None

        # Standard output directory for projects (relative to where main.py or the bundle root is, then one level up to ReifenScanner/projects)
        # For bundled app, projects_dir will be next to the executable's folder.
        # For dev, it will be ReifenScanner/projects
        if getattr(sys, 'frozen', False):
            self.projects_dir = os.path.abspath(os.path.join(os.path.dirname(self.application_path), "projects"))
        else:
            self.projects_dir = os.path.abspath(os.path.join(self.application_path, "../projects"))

        if not os.path.exists(self.projects_dir):
            os.makedirs(self.projects_dir, exist_ok=True)


        # Plans directory (relative to where main.py or the bundle root is)
        self.plans_dir = os.path.join(self.application_path, "plans")
        if not os.path.exists(self.plans_dir):
            # In a bundled app, plans should be copied by PyInstaller.
            # This creation is more for dev mode or if plans are missing.
            os.makedirs(self.plans_dir, exist_ok=True)


        # Progress variable
        self.progress_var = tk.DoubleVar()

        # Initialize managers
        self.plan_manager = PlanManager(self.plans_dir)
        self.project_manager = ProjectManager(self.projects_dir)
        self.subproject_manager = SubprojectManager()
        self.process_queue_manager = ProcessQueueManager()

        # Initialize the panorama processor
        self.processor = PanoramaProcessor(
            self.application_path, # Pass the determined application path
            self.input_base_dir,
            self.projects_dir
        )

        # Load plans and projects
        self.plans = self.plan_manager.load_plans()
        self.projects = self.project_manager.load_projects()

        # Track current selections
        self.current_plan = None
        self.current_project = None
        self.current_subproject = None
        self.current_subproject_type = None  # "frontal", "left", or "right"

        # Track multiple selections
        self.selected_plans = []  # List of selected plan objects
        self.selected_projects = []   # List of selected project objects

        # Setup styling
        self.sidebar_btn_style = {}
        self.sidebar_label_style = {}
        self.listbox_config = {}
        self.canvas_config = {}
        setup_sidebar_styles(self)

        # Create the UI
        create_ui(self)

        # Update gallery with existing projects
        self.update_gallery()

    def update_gallery(self):
        """Update the gallery with tire projects"""
        # Implementation moved to projects_tab.py
        pass

    def is_video_4k(self, video_path):
        """
        Check if the video at the given path has a resolution consistent with 4K.
        A common 4K UHD resolution is 3840x2160. We'll check the height.
        """
        if not video_path or not os.path.exists(video_path):
            return False

        ffprobe_exe_name = "ffprobe.exe"
        # Construct path to ffprobe within the application structure
        # Assumes ffmpeg_bin is at the same level as the directory containing main.py (self.application_path)
        ffprobe_path = os.path.join(self.application_path, "ffmpeg_bin", ffprobe_exe_name)

        if not os.path.exists(ffprobe_path):
            # Fallback: try to find ffprobe in the script's directory (if different or for dev)
            script_dir_ffprobe_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "ffmpeg_bin", ffprobe_exe_name)
            if os.path.exists(script_dir_ffprobe_path):
                ffprobe_path = script_dir_ffprobe_path
            else:
                print(f"Error: {ffprobe_exe_name} not found at {ffprobe_path} or {script_dir_ffprobe_path}")
                # Optionally, could try to use ffprobe from PATH if available
                # For now, return False if not found in expected locations.
                return False

        command = [
            ffprobe_path,
            "-v", "quiet",
            "-print_format", "json",
            "-show_streams",
            video_path
        ]

        try:
            process = subprocess.run(command, capture_output=True, text=True, check=True, creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0)
            data = json.loads(process.stdout)

            for stream in data.get("streams", []):
                if stream.get("codec_type") == "video":
                    height = stream.get("height")
                    # Typical 4K UHD height is 2160. DCI 4K is 4096x2160.
                    # We can check for height being around 2160.
                    if height and isinstance(height, int) and height >= 2000 and height <= 2200: # Allowing some flexibility
                        return True
            return False
        except subprocess.CalledProcessError as e:
            print(f"Error running ffprobe: {e}")
            print(f"ffprobe stderr: {e.stderr}")
            return False
        except json.JSONDecodeError:
            print("Error decoding ffprobe JSON output.")
            return False
        except Exception as e:
            print(f"An unexpected error occurred while checking video resolution: {e}")
            return False

    def select_video(self, subproject_type=None):
        """Select a video file for a subproject"""
        # Use custom video folder if set, otherwise use default path
        if self.custom_video_folder and os.path.exists(self.custom_video_folder):
            initial_dir = self.custom_video_folder
        else:
            initial_dir = os.path.join(self.input_base_dir, "input_videos")
            if not os.path.exists(initial_dir):
                initial_dir = self.input_base_dir

        video_path = filedialog.askopenfilename(
            title=f"Select Video File for {subproject_type}",
            initialdir=initial_dir,
            filetypes=[("Video files", "*.mp4 *.MP4 *.avi *.mov *.mkv"), ("All files", "*.*")]
        )

        if video_path and self.current_project:
            # Update project state and UI
            subproject = self.current_project.subprojects.get(subproject_type)
            if subproject:
                subproject["input_path"] = video_path
                subproject["input_type"] = "video"
                self.project_manager.save_project(self.current_project)
                self.update_subproject_ui(subproject_type)

                # Update status
                self.status_var.set(f"Selected video for {subproject_type}: {os.path.basename(video_path)}")

    def select_image_folder(self, subproject_type=None):
        """Select an image folder for a subproject"""
        # Use custom video folder if set, otherwise use default path
        if self.custom_video_folder and os.path.exists(self.custom_video_folder):
            initial_dir = self.custom_video_folder
        else:
            initial_dir = os.path.join(self.input_base_dir, "input_img")
            if not os.path.exists(initial_dir):
                initial_dir = self.input_base_dir

        folder_path = filedialog.askdirectory(
            title=f"Select Image Folder for {subproject_type}",
            initialdir=initial_dir
        )

        if folder_path and self.current_project:
            # Update project state and UI
            subproject = self.current_project.subprojects.get(subproject_type)
            if subproject:
                subproject["input_path"] = folder_path
                subproject["input_type"] = "folder"
                self.project_manager.save_project(self.current_project)
                self.update_subproject_ui(subproject_type)

                # Update status
                self.status_var.set(f"Selected folder for {subproject_type}: {os.path.basename(folder_path)}")

    def select_subproject(self, subproject_type):
        """
        Set the selected subproject.

        Args:
            subproject_type: Type of subproject ("frontal", "left", or "right")
        """
        # Only proceed if we have a current project
        if not self.current_project:
            return

        # If already selected, do nothing
        if self.selected_subproject == subproject_type:
            return

        # Store new selection
        self.selected_subproject = subproject_type

        # Update UI
        for panel_type, panel in self.subproject_panels.items():
            panel.set_selected(panel_type == subproject_type)
            panel.update()

        # Update status
        self.status_var.set(f"Selected {subproject_type} subproject")


    def start_panorama_process(self, subproject_type=None, continue_from_state=None):
        """Start the panorama generation process for a subproject"""
        if not self.current_project:
            messagebox.showerror("Error", "No project selected.")
            return

        subproject = self.current_project.subprojects.get(subproject_type)
        if not subproject:
            messagebox.showerror("Error", f"Invalid subproject type: {subproject_type}")
            return

        if not subproject.get("input_path"):
            messagebox.showerror("Error", "Please select a video or image folder first.")
            return

        try:
            # Generate/get project directory
            project_dir = os.path.join(self.current_project.project_dir, subproject_type)
            output_dir = os.path.join(project_dir, "output")
            os.makedirs(output_dir, exist_ok=True)

            # Get parameters from the subproject instead of UI variables
            parameters = subproject.get("parameters", {})

            # Prepare parameters
            params = {
                'strip_width': parameters.get("strip_width"),
                'num_samples': parameters.get("num_samples"),
                'max_frames': parameters.get("max_frames"),
                'enable_blending': parameters.get("enable_blending", True),
                'rotate_frames': parameters.get("rotate_frames", True)
            }

            # Add to process queue
            project_info = {
                'name': self.current_project.serial_number,
                'dir': project_dir,
                'id': self.current_project.id
            }

            input_info = {
                'path': subproject["input_path"],
                'type': subproject["input_type"]
            }

            # Add to queue
            task_id = self.process_queue_manager.add_task(
                project_info=project_info,
                subproject_type=subproject_type,
                input_info=input_info,
                params=params,
                continue_from_state=continue_from_state
            )

            # Update subproject status
            subproject["status"] = "queued"
            subproject["task_id"] = task_id
            self.project_manager.save_project(self.current_project)

            # Update status
            self.status_var.set(f"Added {subproject_type} panorama to processing queue")

            # Update the UI
            self.update_current_project_display()

            # Check if we need to start processing (no active process)
            self._check_and_start_next_task()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to start process: {str(e)}")

    def _check_and_start_next_task(self):
        """Check if there's a task to process and start it if needed"""
        try:
            # Get next task from queue
            task = self.process_queue_manager.get_next_task_for_processing()
            if not task:
                return  # No task to process or already processing

            # Dequeue the task and mark it as running
            task_id = task["id"]
            if not self.process_queue_manager.dequeue_and_start_task(task_id):
                return  # Failed to dequeue and start

            # Extract task information
            project_info = task["project_info"]
            subproject_type = task["subproject_type"]
            input_info = task["input_info"]
            params = task["params"]
            continue_from_state = task["continue_from_state"]

            # Update status
            self.status_var.set(f"Processing {subproject_type} panorama for {project_info['name']}...")

            # Try to update cancel button if it exists
            try:
                if hasattr(self, 'cancel_btn'):
                    self.cancel_btn.config(state=tk.NORMAL)
            except Exception as e:
                print(f"Error updating cancel button: {e}")

            # Update progress
            self.progress_var.set(0)

            # Store current subproject type and task ID
            self.current_subproject_type = subproject_type
            self.current_task_id = task_id

            # Reset cancellation flag
            self.processor.reset_cancellation()

            # Add a small delay before starting the process to ensure UI is updated
            # and any previous resources are properly released
            self.root.after(200, lambda: self._start_task_with_delay(
                task_id, project_info, subproject_type, input_info, params, continue_from_state
            ))
        except Exception as e:
            print(f"Error starting next task: {e}")
            traceback.print_exc()
            # Try to mark the task as failed if we have a task_id
            if 'task_id' in locals():
                self.process_queue_manager.set_task_completed(task_id, False, str(e))

    def _start_task_with_delay(self, task_id, project_info, subproject_type, input_info, params, continue_from_state):
        """Start a task after a short delay to ensure proper isolation"""
        try:
            # Start processing thread
            self.processor.start_panorama_process_thread(
                project_info=project_info,
                input_info=input_info,
                params=params,
                continue_from_state=continue_from_state,
                status_callback=lambda *args, **kwargs: self._task_status_callback(task_id, *args, **kwargs),
                progress_callback=lambda progress: self._task_progress_callback(task_id, progress),
                completion_callback=lambda result: self._task_completed_callback(task_id, result, subproject_type, project_info["id"])
            )
        except Exception as e:
            print(f"Error in delayed task start: {e}")
            traceback.print_exc()
            # Mark the task as failed
            self.process_queue_manager.set_task_completed(task_id, False, str(e))

    def _task_status_callback(self, task_id, *args, **kwargs):
        """Status callback for a task"""
        # Just pass through to the regular status update function
        update_status(self, *args, **kwargs)

    def _task_progress_callback(self, task_id, progress):
        """Progress callback for a task"""
        # Store the last progress value to avoid excessive updates
        if not hasattr(self, '_last_progress_values'):
            self._last_progress_values = {}

        # Get the last progress value for this task
        last_progress = self._last_progress_values.get(task_id, 0)

        # Only update if progress has changed significantly (5% or more)
        # This reduces UI updates and prevents flickering
        if abs(progress - last_progress) >= 5 or progress >= 100 or progress == 0:
            # Update the task progress in the queue manager
            self.process_queue_manager.update_task_progress(task_id, progress)

            # Also update the main progress bar
            update_progress(self, progress)

            # Store the new progress value
            self._last_progress_values[task_id] = progress

    def _task_completed_callback(self, task_id, result, subproject_type, project_id):
        """Completion callback for a task"""
        try:
            # Mark task as completed
            self.process_queue_manager.set_task_completed(task_id, result is not None)

            # Update the project's subproject status
            project = self.project_manager.get_project(project_id)
            if project:
                subproject = project.subprojects.get(subproject_type)
                if subproject:
                    # Check for panorama existence
                    panorama_exists = False
                    output_dir = os.path.join(project.project_dir, subproject_type, "output")

                    # First check if the subproject has a get_panorama_path method
                    if hasattr(subproject, 'get_panorama_path') and callable(subproject.get_panorama_path):
                        # Use the subproject's method to check for panorama
                        panorama_path = subproject.get_panorama_path()
                        if panorama_path:
                            panorama_exists = True
                    else:
                        # For performance, just check the most common formats
                        # and assume success if the process reported success
                        if result:
                            # If the process reported success, trust it
                            panorama_exists = True
                        else:
                            # Only check files if result is False
                            try:
                                # Check for standard JPG (most common)
                                standard_path = os.path.join(output_dir, "tire_unwrapped_full.JPG")
                                if os.path.exists(standard_path):
                                    panorama_exists = True
                                else:
                                    # Check for PNG format
                                    png_path = os.path.join(output_dir, "tire_unwrapped_full.png")
                                    if os.path.exists(png_path):
                                        panorama_exists = True
                                    else:
                                        # Check for lowercase jpg
                                        jpg_path = os.path.join(output_dir, "tire_unwrapped_full.jpg")
                                        if os.path.exists(jpg_path):
                                            panorama_exists = True
                                        else:
                                            # Check for enhanced JPG
                                            enhanced_path = os.path.join(output_dir, "tire_unwrapped_enhanced.JPG")
                                            if os.path.exists(enhanced_path):
                                                panorama_exists = True
                            except Exception:
                                # If any error occurs during file checking, don't fail the process
                                pass

                    # If panorama exists, mark as completed regardless of the result parameter
                    if panorama_exists:
                        # Success - panorama exists
                        subproject["status"] = "completed"
                        subproject["completed_date"] = datetime.now().isoformat()
                        self.status_var.set(f"{subproject_type} panorama for {project.serial_number} completed successfully!")

                        # Automatically clean up frames after successful completion
                        self._auto_cleanup_frames(project, subproject_type)
                    elif self.processor.is_process_cancelled():
                        # Cancelled - make sure it's marked as cancelled, not failed
                        subproject["status"] = "cancelled"
                        self.status_var.set(f"{subproject_type} panorama process for {project.serial_number} cancelled. You can continue later.")
                    elif result:
                        # Process reported success but no panorama found - this is an inconsistency
                        # Mark it as cancelled instead of failed since we can't find the panorama but want to allow continuation
                        subproject["status"] = "cancelled"
                        self.status_var.set(f"{subproject_type} panorama process for {project.serial_number} reported success but no panorama found. You can continue later.")
                    else:
                        # Failed - mark as cancelled to allow continuation
                        subproject["status"] = "cancelled"
                        self.status_var.set(f"{subproject_type} panorama process for {project.serial_number} did not complete successfully. You can continue later.")

                    # Remove task ID
                    if "task_id" in subproject:
                        del subproject["task_id"]

                    # Save project state
                    self.project_manager.save_project(project)

                    # Force a refresh of the current project tab if this is the currently displayed project
                    # Schedule the update with a slight delay to ensure all processing is complete
                    if hasattr(self, 'current_project') and self.current_project and self.current_project.id == project_id:
                        # Update the current project data in memory to reflect the changes
                        self.current_project = self.project_manager.get_project(project_id)
                        self.root.after(100, self._force_current_project_tab_refresh)
                    else:
                        # If it's not the current project, just update the project data in memory
                        # so it will be correct when the user switches to it
                        pass

            # Try to reset cancel button if it exists
            try:
                if hasattr(self, 'cancel_btn'):
                    self.cancel_btn.config(state=tk.DISABLED)
            except:
                pass

            # Reset current task info
            self.current_subproject_type = None
            self.current_task_id = None

            # Start next task if available - with a small delay to allow the UI to update
            self.root.after(100, self._check_and_start_next_task)
        except Exception as e:
            print(f"Error in task completion callback: {e}")
            # Try to start next task anyway
            self.root.after(100, self._check_and_start_next_task)

    def subproject_completed(self, result, subproject_type):
        """
        Legacy method for handling subproject completion
        Now handled by the task completion callback
        """
        # This method is kept for backwards compatibility
        # The functionality has been moved to _task_completed_callback
        pass

    def cancel_queue_task(self, task_id):
        """
        Cancel a task in the queue.

        This method coordinates between the process queue manager, project state,
        and UI to ensure consistent state management when cancelling tasks.

        Args:
            task_id: ID of the task to cancel

        Returns:
            True if cancellation was successful, False otherwise
        """
        # Get task details before cancellation
        if not hasattr(self, 'process_queue_manager'):
            return False

        # Get task information
        task = self.process_queue_manager.get_task(task_id)
        if not task:
            return False

        # Extract project and subproject information
        project_id = task.get("project_info", {}).get("id")
        subproject_type = task.get("subproject_type")

        if not project_id or not subproject_type:
            return False

        # Get project object
        project = self.project_manager.get_project(project_id)
        if not project:
            return False

        # Get status string for comparison
        status = task["status"]
        status_str = status.name if hasattr(status, "name") else str(status)

        # Try to cancel the task in the queue
        success, message = self.process_queue_manager.cancel_task(task_id)

        if not success:
            self.status_var.set(message)
            return False

        # If it's a running task, we need to use the main cancellation method
        if status_str == "RUNNING":
            if hasattr(self, 'current_task_id') and self.current_task_id == task_id:
                self.cancel_current_process()
                self.status_var.set(f"Cancelling task...")
                return True
            else:
                # Task is marked as running but not in our current task, something is wrong
                self.status_var.set("Cannot cancel task - inconsistent state")
                return False

        # For queued tasks, we need to update the project state
        elif status_str == "QUEUED":
            # Update subproject status in the project data
            if project.subprojects and subproject_type in project.subprojects:
                # Set status to "not_started" instead of "cancelled" to allow fresh restart
                # This preserves any existing processing_state.json file for future continuation
                project.subprojects[subproject_type]["status"] = "not_started"

                # Remove task_id reference
                if "task_id" in project.subprojects[subproject_type]:
                    del project.subprojects[subproject_type]["task_id"]

                # Save the project
                self.project_manager.save_project(project)

                # Update UI
                if hasattr(self, 'update_current_project_display') and self.current_project and self.current_project.id == project_id:
                    self.update_current_project_display()

                self.status_var.set(f"Removed {subproject_type} task from queue")
                return True

        # For already cancelled tasks, just return success
        elif status_str == "CANCELLED":
            return True

        return False

    def cancel_current_process(self, callback=None):
        """
        Cancel the current processing operation.

        This method ensures proper coordination between the process,
        queue, and project state when cancelling a running process.

        Args:
            callback: Optional function to call after cancellation completes
        """
        try:
            # Cancel the actual process
            self.processor.cancel_current_process()
            self.status_var.set("Cancelling process...")

            # Get the current task information
            current_task_id = None
            current_subproject_type = None

            if hasattr(self, 'current_task_id') and self.current_task_id:
                current_task_id = self.current_task_id

                # Get the task from the queue manager
                if hasattr(self, 'process_queue_manager'):
                    task = self.process_queue_manager.get_task(current_task_id)
                    if task:
                        current_subproject_type = task.get("subproject_type")

            # If we couldn't get the task from the queue, try the current subproject type
            if not current_subproject_type and hasattr(self, 'current_subproject_type'):
                current_subproject_type = self.current_subproject_type

            # Update the project state if we have a current project and subproject type
            if current_subproject_type and hasattr(self, 'current_project') and self.current_project:
                subproject = self.current_project.subprojects.get(current_subproject_type)
                if subproject:
                    # Save the input path and input type before resetting
                    input_path = subproject.get("input_path")
                    input_type = subproject.get("input_type")

                    # Reset the subproject to initial state while preserving input
                    subproject.clear()
                    subproject["status"] = "cancelled"
                    subproject["input_path"] = input_path
                    subproject["input_type"] = input_type
                    subproject["parameters"] = {}

                    # Save the project with the reset subproject
                    self.project_manager.save_project(self.current_project)

                    # Log the reset
                    print(f"Reset subproject {current_subproject_type} while preserving input path: {input_path}")

            # Mark the task as cancelled in the queue
            if current_task_id and hasattr(self, 'process_queue_manager'):
                self.process_queue_manager.manually_mark_task_cancelled(
                    current_task_id, "Cancelled by user"
                )

            # Pass the callback to the process manager
            from gui.process_manager import cancel_processing
            cancel_processing(self, callback=callback)

            # Disable cancel button
            if hasattr(self, 'cancel_btn'):
                self.cancel_btn.config(state=tk.DISABLED)

            # Update the UI
            if hasattr(self, 'update_current_project_display'):
                self.update_current_project_display()

            # Update queue display if it exists
            if hasattr(self, 'queue_display'):
                self.queue_display.update_display()

        except Exception as e:
            print(f"Error in cancel_current_process: {e}")
            import traceback
            traceback.print_exc()

    def _auto_cleanup_frames(self, project, subproject_type):
        """Automatically clean up frames after panorama completion without confirmation"""
        subproject = project.subprojects.get(subproject_type)
        if not subproject or subproject.get("status") != "completed":
            return

        # Perform cleanup without confirmation
        frames_dir = os.path.join(project.project_dir, subproject_type, "frames")
        if not os.path.exists(frames_dir):
            return

        try:
            # Count files for status message
            file_count = sum(1 for file in os.listdir(frames_dir) if os.path.isfile(os.path.join(frames_dir, file)))

            # Log the cleanup
            print(f"Auto-cleaning {file_count} frames for {subproject_type} panorama in project {project.serial_number}")

            # Clean up frames
            frames_success = self._perform_cleanup(project, subproject_type, frames_dir)

            # Also clean up temporary output files
            output_dir = os.path.join(project.project_dir, subproject_type, "output")
            output_success = self._cleanup_temporary_output_files(project, subproject_type, output_dir)

            return frames_success and output_success
        except Exception as e:
            print(f"Error in auto cleanup: {e}")
            return False

    def cleanup_frames(self, subproject_type):
        """Delete frames and temporary output files after panorama is completed (manual cleanup with confirmation)"""
        if not self.current_project:
            return

        subproject = self.current_project.subprojects.get(subproject_type)
        if not subproject or subproject.get("status") != "completed":
            messagebox.showinfo("Info", "Can only clean up frames for completed panoramas.")
            return

        # Confirm cleanup
        confirm = messagebox.askyesno(
            "Confirm Cleanup",
            f"This will delete all frames and temporary files for the {subproject_type} panorama.\n"
            "Only the final panorama files will be preserved. Continue?"
        )

        if not confirm:
            return

        # Perform frames cleanup
        frames_dir = os.path.join(self.current_project.project_dir, subproject_type, "frames")
        frames_success = True
        if os.path.exists(frames_dir):
            frames_success = self._perform_cleanup(self.current_project, subproject_type, frames_dir)

        # Perform output directory cleanup
        output_dir = os.path.join(self.current_project.project_dir, subproject_type, "output")
        output_success = True
        if os.path.exists(output_dir):
            output_success = self._cleanup_temporary_output_files(self.current_project, subproject_type, output_dir)

        # Update status based on results
        if frames_success and output_success:
            self.status_var.set(f"Cleaned up frames and temporary files for {subproject_type} panorama.")
        elif not frames_success and not output_success:
            messagebox.showerror("Error", "Failed to clean up frames and temporary files. See logs for details.")
        elif not frames_success:
            messagebox.showerror("Error", "Failed to clean up frames. See logs for details.")
        elif not output_success:
            messagebox.showerror("Error", "Failed to clean up temporary files. See logs for details.")
        else:
            messagebox.showinfo("Info", "No files found to clean up.")

    def _perform_cleanup(self, project, subproject_type, frames_dir):
        """Perform the actual cleanup of frames"""
        try:
            # Delete all files in frames directory
            for file in os.listdir(frames_dir):
                file_path = os.path.join(frames_dir, file)
                if os.path.isfile(file_path):
                    os.remove(file_path)

            # Update project state
            subproject = project.subprojects.get(subproject_type)
            if subproject:
                subproject["frames_cleaned"] = True
                self.project_manager.save_project(project)

            # Update UI if this is the current project
            if hasattr(self, 'current_project') and self.current_project and self.current_project.id == project.id:
                self.update_subproject_ui(subproject_type)

            return True
        except Exception as e:
            print(f"Error cleaning up frames: {e}")
            return False

    def _cleanup_temporary_output_files(self, project, subproject_type, output_dir):
        """Clean up temporary files in the output directory, keeping only the final panorama files

        Args:
            project: Project object
            subproject_type: Type of subproject ("frontal", "left", "right")
            output_dir: Path to the output directory

        Returns:
            True if successful, False otherwise
        """
        if not os.path.exists(output_dir):
            return True  # Nothing to clean up

        try:
            # Find the final panorama files that we want to keep
            files_to_keep = []

            # Look for panorama files with the new naming pattern
            for file in os.listdir(output_dir):
                # Keep all files that end with _full.* or _enhanced.*
                if (file.startswith("tire_") and
                    ("_full." in file or "_enhanced." in file) and
                    any(file.endswith(ext) for ext in [".JPG", ".jpg", ".png", ".tiff", ".tif"])):
                    full_path = os.path.join(output_dir, file)
                    if os.path.isfile(full_path):
                        files_to_keep.append(full_path)

            # Also keep log files
            for file in os.listdir(output_dir):
                if file.endswith(".log"):
                    log_path = os.path.join(output_dir, file)
                    if os.path.isfile(log_path):
                        files_to_keep.append(log_path)

            # If we didn't find any final panorama files, don't delete anything
            if not files_to_keep:
                print(f"Warning: No final panorama files found in {output_dir}, skipping cleanup")
                return True

            # Count files for status message
            deleted_count = 0

            # Delete all files except the ones we want to keep
            for file in os.listdir(output_dir):
                file_path = os.path.join(output_dir, file)

                # Skip directories
                if not os.path.isfile(file_path):
                    continue

                # Skip files we want to keep
                if file_path in files_to_keep:
                    continue

                # Delete the file
                try:
                    os.remove(file_path)
                    deleted_count += 1
                except Exception as e:
                    print(f"Error deleting file {file_path}: {e}")

            # Log the cleanup
            print(f"Cleaned up {deleted_count} temporary files in output directory for {subproject_type} panorama")

            return True
        except Exception as e:
            print(f"Error cleaning up temporary output files: {e}")
            import traceback
            traceback.print_exc()
            return False

    def update_subproject_ui(self, subproject_type):
        """Update UI for a specific subproject"""
        # This will be implemented in the current_project_tab.py
        # But we need this method as a hook
        pass

    def toggle_sidebar(self):
        """Toggle sidebar visibility"""
        if self.sidebar_open:
            self.sidebar.pack_forget()
            self.toggle_btn.config(text="☰")  # Hamburger icon when closed
            self.sidebar_open = False
        else:
            self.sidebar.pack(fill=tk.Y, side=tk.LEFT)
            self.toggle_btn.config(text="≡")  # Different icon when open
            self.sidebar_open = True

    def select_video_folder(self):
        """Allow user to select a custom video folder to use as the standard path"""
        # Use the current custom folder as initial dir if it exists
        initial_dir = self.custom_video_folder if self.custom_video_folder and os.path.exists(self.custom_video_folder) else self.input_base_dir

        folder_path = filedialog.askdirectory(
            title="Select Standard Video Folder",
            initialdir=initial_dir
        )

        if folder_path:
            self.custom_video_folder = folder_path
            self.status_var.set(f"Set standard path to: {folder_path}")

    def browse_executable(self):
        """Allow user to browse for the C++ executable"""
        executable_path = filedialog.askopenfilename(
            title="Select C++ Tire Stitcher Executable",
            filetypes=[("Executable files", "*"), ("All files", "*.*")]
        )

        if executable_path:
            # Check if file is executable
            if os.access(executable_path, os.X_OK):
                self.executable_path_var.set(executable_path)
                self.processor.cpp_executable = executable_path
            else:
                messagebox.showerror(
                    "Error",
                    f"The selected file '{os.path.basename(executable_path)}' is not executable. Please choose a valid executable file."
                )

    def _force_current_project_tab_refresh(self):
        """Force a complete refresh of the Current Tire tab"""
        # First check if we have the update method
        if not hasattr(self, 'update_current_project_display'):
            return

        # Call the update method to refresh the tab
        self.update_current_project_display()

        # The update_current_project_display method already handles updating all panels
        # No need for additional panel updates here

        # Get the current tab
        current_tab_id = self.notebook.select()
        current_tab_name = self.notebook.tab(current_tab_id, "text")

        # If we're not on the Current Tire tab, switch to it to force a refresh
        if current_tab_name != "Current Tire":
            # Find the Current Tire tab index
            for i, tab_id in enumerate(self.notebook.tabs()):
                if self.notebook.tab(tab_id, "text") == "Current Tire":
                    # Select the tab to force a refresh
                    self.notebook.select(i)
                    break

            # Switch back to the original tab after a short delay
            self.root.after(100, lambda: self.notebook.select(current_tab_id))

    def on_tab_selected(self, event):
        """Handle tab selection"""
        # Get selected tab
        tab_id = event.widget.select()
        tab_name = event.widget.tab(tab_id, "text")

        # If selecting Current Tire tab, update display
        if tab_name == "Current Tire" and hasattr(self, 'update_current_project_display'):
            self.update_current_project_display()